<?php

namespace Plugins\Meetings\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

use Illuminate\Support\Facades\Log;
use Plugins\Meetings\Models\Meeting;
use Plugins\Meetings\Models\MeetingParticipant;

class MeetingController extends Controller
{
    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = auth()->user();

            if (!$user || !$user->role) {
                abort(403, 'Access denied. No role assigned.');
            }

            $action = $request->route()->getActionMethod();
            $requiredPermission = $this->getRequiredPermission($action);

            if ($requiredPermission && !$user->hasPermission($requiredPermission)) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Get required permission for action
     */
    private function getRequiredPermission(string $action): ?string
    {
        return match($action) {
            'index', 'show', 'apiList' => 'view_meetings',
            'store' => 'create_meetings',
            'update', 'addParticipant', 'removeParticipant' => 'edit_meetings',
            'destroy' => 'delete_meetings',
            default => 'view_meetings'
        };
    }

    /**
     * Display the meetings list
     */
    public function index(): View
    {
        $user = auth()->user();
        
        $meetings = Meeting::with(['participants', 'attachments', 'actionItems'])
            ->where(function($query) use ($user) {
                $query->where('created_by', $user->id)
                      ->orWhereHas('participants', function($q) use ($user) {
                          $q->where('user_id', $user->id);
                      });
            })
            ->orderBy('scheduled_at', 'desc')
            ->get();

        $stats = [
            'total' => $meetings->count(),
            'upcoming' => $meetings->where('scheduled_at', '>', now())->count(),
            'past' => $meetings->where('scheduled_at', '<=', now())->count(),
            'with_action_items' => $meetings->filter(fn($meeting) => $meeting->actionItems->count() > 0)->count(),
        ];

        return view('plugins.meetings::index', compact('meetings', 'stats'));
    }

    /**
     * Store a newly created meeting
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'agenda' => 'nullable|string',
            'scheduled_at' => 'required|date|after:now',
            'duration_minutes' => 'nullable|integer|min:15|max:480',
            'location' => 'nullable|string|max:255',
            'meeting_link' => 'nullable|url',
            'participants' => 'nullable|array',
        ]);

        try {
            $user = auth()->user();
            
            $meeting = Meeting::create([
                'title' => $request->title,
                'description' => $request->description,
                'agenda' => $request->agenda,
                'scheduled_at' => $request->scheduled_at,
                'duration_minutes' => $request->duration_minutes ?? 60,
                'location' => $request->location,
                'meeting_link' => $request->meeting_link,
                'created_by' => $user->id,
                'status' => 'scheduled',
            ]);

            // Add participants
            if ($request->participants) {
                foreach ($request->participants as $participant) {
                    if (is_array($participant) && isset($participant['type']) && $participant['type'] === 'new_contact') {
                        // Create new contact if business plugin is available
                        if (class_exists('\Plugins\Business\Models\Contact')) {
                            try {
                                $contact = \Plugins\Business\Models\Contact::create([
                                    'name' => $participant['name'],
                                    'email' => $participant['email'],
                                    'business_id' => null, // No business association for now
                                    'is_primary' => false,
                                ]);

                                // Add contact as participant (using email since contacts don't have user_id)
                                $meeting->participants()->create([
                                    'user_id' => null,
                                    'contact_email' => $participant['email'],
                                    'contact_name' => $participant['name'],
                                    'status' => 'invited',
                                ]);
                            } catch (\Exception $e) {
                                Log::warning("Failed to create contact for meeting participant: " . $e->getMessage());
                                // Still add as participant with email only
                                $meeting->participants()->create([
                                    'user_id' => null,
                                    'contact_email' => $participant['email'],
                                    'contact_name' => $participant['name'],
                                    'status' => 'invited',
                                ]);
                            }
                        }
                    } else {
                        // Handle existing user or contact
                        $participantId = is_array($participant) ? $participant['id'] : $participant;

                        if (strpos($participantId, 'contact_') === 0) {
                            // This is a business contact
                            $contactId = str_replace('contact_', '', $participantId);
                            if (class_exists('\Plugins\Business\Models\Contact')) {
                                $contact = \Plugins\Business\Models\Contact::find($contactId);
                                if ($contact) {
                                    $meeting->participants()->create([
                                        'user_id' => null,
                                        'contact_email' => $contact->email,
                                        'contact_name' => $contact->name,
                                        'status' => 'invited',
                                    ]);
                                }
                            }
                        } else {
                            // This is a system user
                            $meeting->participants()->create([
                                'user_id' => $participantId,
                                'status' => 'invited',
                            ]);
                        }
                    }
                }
            }

            // Always add creator as participant if not already included
            if (!$meeting->participants()->where('user_id', $user->id)->exists()) {
                $meeting->participants()->create([
                    'user_id' => $user->id,
                    'status' => 'accepted',
                ]);
            }

            Log::info("Meeting '{$meeting->title}' created", [
                'user' => $user->email,
                'meeting_id' => $meeting->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Meeting created successfully!',
                'meeting' => $meeting->load(['participants', 'attachments', 'actionItems'])
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to create meeting: " . $e->getMessage(), [
                'exception' => $e,
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create meeting: ' . $e->getMessage(),
                'error' => config('app.debug') ? $e->getTraceAsString() : null
            ], 500);
        }
    }

    /**
     * Display the specified meeting
     */
    public function show(Meeting $meeting): View
    {
        $meeting->load(['participants', 'attachments', 'actionItems.assignee', 'creator']);
        
        return view('plugins.meetings::show', compact('meeting'));
    }

    /**
     * Update the specified meeting
     */
    public function update(Request $request, Meeting $meeting): JsonResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'agenda' => 'nullable|string',
            'minutes' => 'nullable|string',
            'scheduled_at' => 'required|date',
            'duration_minutes' => 'nullable|integer|min:15|max:480',
            'location' => 'nullable|string|max:255',
            'meeting_link' => 'nullable|url',
            'status' => 'nullable|in:scheduled,in_progress,completed,cancelled',
        ]);

        try {
            $meeting->update($request->only([
                'title', 'description', 'agenda', 'minutes', 'scheduled_at',
                'duration_minutes', 'location', 'meeting_link', 'status'
            ]));

            Log::info("Meeting '{$meeting->title}' updated", [
                'user' => auth()->user()->email,
                'meeting_id' => $meeting->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Meeting updated successfully!',
                'meeting' => $meeting->load(['participants', 'attachments', 'actionItems'])
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to update meeting: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update meeting: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified meeting
     */
    public function destroy(Meeting $meeting): JsonResponse
    {
        try {
            $meetingTitle = $meeting->title;
            $meeting->delete();

            Log::info("Meeting '{$meetingTitle}' deleted", [
                'user' => auth()->user()->email,
                'meeting_id' => $meeting->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Meeting deleted successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to delete meeting: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete meeting: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get meetings as JSON for AJAX requests
     */
    public function apiList(Request $request): JsonResponse
    {
        $user = auth()->user();
        
        $query = Meeting::with(['participants', 'attachments', 'actionItems'])
            ->where(function($q) use ($user) {
                $q->where('created_by', $user->id)
                  ->orWhereHas('participants', function($subQ) use ($user) {
                      $subQ->where('user_id', $user->id);
                  });
            });
        
        // Filter by status if requested
        if ($request->has('status') && $request->status !== 'all') {
            if ($request->status === 'upcoming') {
                $query->where('scheduled_at', '>', now());
            } elseif ($request->status === 'past') {
                $query->where('scheduled_at', '<=', now());
            } else {
                $query->where('status', $request->status);
            }
        }
        
        // Filter by date range if requested
        if ($request->has('date_from') && $request->date_from) {
            $query->where('scheduled_at', '>=', $request->date_from);
        }
        
        if ($request->has('date_to') && $request->date_to) {
            $query->where('scheduled_at', '<=', $request->date_to);
        }
        
        $meetings = $query->orderBy('scheduled_at', 'desc')->get();
        
        return response()->json([
            'success' => true,
            'meetings' => $meetings
        ]);
    }

    /**
     * Add participant to meeting
     */
    public function addParticipant(Request $request, Meeting $meeting): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        try {
            if ($meeting->participants()->where('user_id', $request->user_id)->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'User is already a participant in this meeting.'
                ], 400);
            }

            $participant = $meeting->participants()->create([
                'user_id' => $request->user_id,
                'status' => 'invited',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Participant added successfully!',
                'participant' => $participant->load('user')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add participant: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove participant from meeting
     */
    public function removeParticipant(Meeting $meeting, MeetingParticipant $participant): JsonResponse
    {
        try {
            if ($participant->meeting_id !== $meeting->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Participant does not belong to this meeting.'
                ], 400);
            }

            $participant->delete();

            return response()->json([
                'success' => true,
                'message' => 'Participant removed successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove participant: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search users and contacts for participant selection
     */
    public function searchUsers(Request $request): JsonResponse
    {
        try {
            $query = $request->get('q', '');
            $results = [];
            $currentUser = auth()->user();

            // Search users (system-wide)
            $users = \App\Models\User::where(function($q) use ($query) {
                if ($query) {
                    $q->where('name', 'LIKE', "%{$query}%")
                      ->orWhere('email', 'LIKE', "%{$query}%");
                }
            })
            ->active()
            ->select('id', 'name', 'email')
            ->limit(10)
            ->get();

            foreach ($users as $user) {
                $results[] = [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'type' => 'user',
                    'source' => 'System User',
                    'display_name' => $user->name,
                    'contact_info' => $user->email
                ];
            }

            // Search business contacts if business plugin is available
            if (class_exists('\Plugins\Business\Models\Contact')) {
                // Get businesses the current user has access to
                $accessibleBusinessIds = [];

                // If user has manage_businesses permission, they can see all contacts
                if ($currentUser->hasPermission('manage_businesses')) {
                    $accessibleBusinessIds = \Plugins\Business\Models\Business::pluck('id')->toArray();
                } else {
                    // Otherwise, only contacts from businesses they're assigned to
                    $accessibleBusinessIds = $currentUser->businesses()->pluck('businesses.id')->toArray();

                    // Also include businesses they created
                    $createdBusinessIds = $currentUser->createdBusinesses()->pluck('id')->toArray();
                    $accessibleBusinessIds = array_merge($accessibleBusinessIds, $createdBusinessIds);
                    $accessibleBusinessIds = array_unique($accessibleBusinessIds);
                }

                $contactsQuery = \Plugins\Business\Models\Contact::with('business')
                    ->where(function($q) use ($query) {
                        if ($query) {
                            $q->where('name', 'LIKE', "%{$query}%")
                              ->orWhere('arabic_name', 'LIKE', "%{$query}%")
                              ->orWhere('email', 'LIKE', "%{$query}%")
                              ->orWhere('phone', 'LIKE', "%{$query}%")
                              ->orWhere('phone2', 'LIKE', "%{$query}%");
                        }
                    })
                    ->whereNotNull('email') // Require email for meeting invitations
                    ->select('id', 'name', 'arabic_name', 'email', 'phone', 'phone2', 'phone_ext', 'phone2_ext', 'position', 'business_id');

                // Filter by accessible businesses if user doesn't have global permissions
                if (!empty($accessibleBusinessIds)) {
                    $contactsQuery->whereIn('business_id', $accessibleBusinessIds);
                }

                $contacts = $contactsQuery->limit(15)->get();

                foreach ($contacts as $contact) {
                    // Build display name with Arabic name if available
                    $displayName = $contact->name;
                    if ($contact->arabic_name) {
                        $displayName .= " ({$contact->arabic_name})";
                    }

                    // Build contact info string
                    $contactInfo = $contact->email;
                    if ($contact->phone) {
                        $contactInfo .= ' • ' . $contact->phone;
                        if ($contact->phone_ext) {
                            $contactInfo .= ' ext. ' . $contact->phone_ext;
                        }
                    }
                    if ($contact->phone2) {
                        $contactInfo .= ' • ' . $contact->phone2;
                        if ($contact->phone2_ext) {
                            $contactInfo .= ' ext. ' . $contact->phone2_ext;
                        }
                    }

                    $results[] = [
                        'id' => 'contact_' . $contact->id,
                        'name' => $contact->name,
                        'arabic_name' => $contact->arabic_name,
                        'email' => $contact->email,
                        'phone' => $contact->phone,
                        'phone2' => $contact->phone2,
                        'position' => $contact->position,
                        'type' => 'contact',
                        'source' => $contact->business ? $contact->business->name : 'Business Contact',
                        'business_id' => $contact->business_id,
                        'display_name' => $displayName,
                        'contact_info' => $contactInfo
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'users' => $results
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to search users: ' . $e->getMessage()
            ], 500);
        }
    }
}
